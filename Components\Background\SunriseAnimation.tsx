import React, { useLayoutEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { gsap } from 'gsap';

// Interface pour les méthodes exposées du composant
export interface SunriseAnimationRef {
  triggerSunrise: () => void;
  resetSun: () => void;
}

// Interface pour les props du composant
interface SunriseAnimationProps {
  isVisible: boolean; // Contrôle la visibilité du composant
}

const SunriseAnimation = forwardRef<SunriseAnimationRef, SunriseAnimationProps>(
  ({ isVisible }, ref) => {
    // Références pour les éléments DOM - VERSION SIMPLIFIÉE
    const containerRef = useRef<HTMLDivElement>(null);
    const sunWrapperRef = useRef<HTMLDivElement>(null);
    const sunGlowRef = useRef<HTMLDivElement>(null);
    const lensFlareRef = useRef<HTMLDivElement>(null);
    const sunImageRef = useRef<HTMLImageElement>(null);

    // Référence pour la timeline GSAP
    const timelineRef = useRef<gsap.core.Timeline | null>(null);

    // 🌅 FONCTION PRINCIPALE: Déclencher l'animation de lever de soleil - VERSION SIMPLIFIÉE
    const triggerSunrise = () => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn('🌅 Éléments DOM non prêts pour l\'animation');
        return;
      }

      console.log('🌅 Déclenchement de l\'animation de lever de soleil - Version réaliste');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation de lever de soleil terminée');
        }
      });

      // Position initiale - soleil simple
      gsap.set(sunWrapperRef.current, {
        y: '60%',
        opacity: 1
      });
      gsap.set(sunGlowRef.current, {
        opacity: 0,
        scale: 0.8
      });
      gsap.set(lensFlareRef.current, {
        opacity: 0,
        rotation: 0 // CISCO: Position initiale pour rotation (plus besoin de y car dans le même conteneur)
      });

      // PHASE 1: Le soleil monte ENCORE PLUS HAUT sur l'horizon - CISCO
      timelineRef.current.fromTo(
        sunWrapperRef.current,
        { y: '60%' },
        {
          y: '-25%', // CISCO: ENCORE un peu plus haut pour finir la course
          duration: 12.0,
          ease: 'power1.out'
        },
        0
      );

      // PHASE 2: La lueur EXTRA LUMINEUX apparaît
      timelineRef.current.fromTo(
        sunGlowRef.current,
        { opacity: 0, scale: 0.8 },
        {
          opacity: 1.0, // CISCO: Opacité maximale pour visibilité
          scale: 1.2,   // CISCO: Plus grand pour plus d'impact
          duration: 8.0,
          ease: 'power2.out'
        },
        2
      );

      // PHASE 3: Le lens flare apparaît - CISCO: Plus besoin d'animation de position car dans le même conteneur !
      timelineRef.current.fromTo(
        lensFlareRef.current,
        {
          opacity: 0,
          rotation: 0
        },
        {
          opacity: 0.7, // CISCO: Visible mais subtil pour l'illusion
          duration: 8.0, // CISCO: Apparition progressive
          ease: 'power2.out'
        },
        2 // CISCO: Démarre après 2 secondes
      );

      // PHASE 4: Rotation TRÈS LENTE continue du lens-flare - CISCO: Rayons qui tournent
      timelineRef.current.to(
        lensFlareRef.current,
        {
          rotation: 360,
          duration: 120.0, // CISCO: 2 minutes pour une rotation complète = TRÈS LENT
          ease: 'none',
          repeat: -1 // CISCO: Rotation infinie pour l'illusion des rayons
        },
        4 // CISCO: Démarre la rotation après 4 secondes (quand le lens-flare est visible)
      );
    };

    // 🔄 FONCTION: Remettre le soleil en position initiale - VERSION SIMPLIFIÉE
    const resetSun = () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      if (sunWrapperRef.current && sunGlowRef.current && lensFlareRef.current) {
        gsap.set(sunWrapperRef.current, {
          y: '60%',
          opacity: 1
        });
        gsap.set(sunGlowRef.current, {
          opacity: 0,
          scale: 0.8
        });
        gsap.set(lensFlareRef.current, {
          opacity: 0,
          rotation: 0 // CISCO: Reset rotation (plus besoin de y car dans le même conteneur)
        });
      }

      console.log('🔄 Soleil remis en position initiale');
    };

    // Exposer les méthodes via useImperativeHandle
    useImperativeHandle(ref, () => ({
      triggerSunrise,
      resetSun
    }));

    // Cleanup à la destruction du composant
    useLayoutEffect(() => {
      return () => {
        if (timelineRef.current) {
          timelineRef.current.kill();
        }
      };
    }, []);

    // Ne pas rendre si non visible
    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={containerRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 1.8 }} // 🔧 CISCO: Soleil reste derrière les collines (z-index 5) - NE PAS TOUCHER
      >
        {/* Conteneur pour le soleil et ses effets - CISCO: Soleil AGRANDI avec lens-flare intégré */}
        <div
          ref={sunWrapperRef}
          className="absolute w-52 h-52 left-1/2 top-1/2 -translate-x-1/2"
          style={{
            transform: 'translateX(-50%) translateY(60%)', // Position initiale plus haute (100px de moins)
          }}
        >
          <div className="relative w-full h-full">
            {/* EFFET 1: Lueur subtile du soleil */}
            <div
              ref={sunGlowRef}
              className="sun-glow absolute inset-0 opacity-0"
            />

            {/* EFFET 2: Lens Flare PNG - CISCO: Maintenant DANS le conteneur du soleil pour synchronisation parfaite */}
            <div
              ref={lensFlareRef}
              className="absolute opacity-0 pointer-events-none"
              style={{
                width: '600px',   // CISCO: Plus grand pour couvrir le soleil agrandi
                height: 'auto',   // CISCO: Garde les proportions
                left: '50%',      // CISCO: Centré sur le soleil
                top: '50%',       // CISCO: Centré sur le soleil
                transform: 'translate(-50%, -50%)',
                transformOrigin: 'center center', // CISCO: Rotation autour du centre
                zIndex: 15 // CISCO: Au-dessus du soleil pour les rayons
              }}
            >
              <img
                src="/lens-flare-light-3508x2540.png"
                alt="Lens Flare"
                className="w-full h-auto"
                style={{
                  mixBlendMode: 'screen' // CISCO: Élimine le fond noir
                }}
              />
            </div>

            {/* L'image du soleil - CISCO: BEAUCOUP plus lumineux avec lueur diffuse */}
            <img
              ref={sunImageRef}
              src="/SUN.png"
              alt="Soleil"
              className="relative z-10 w-full h-full object-contain"
              style={{
                filter: 'brightness(2.5) contrast(1.5) saturate(1.6) drop-shadow(0 0 40px rgba(255, 255, 255, 1.0)) drop-shadow(0 0 80px rgba(255, 220, 0, 0.8)) drop-shadow(0 0 120px rgba(255, 200, 0, 0.6))'
              }}
            />
          </div>
        </div>
      </div>
    );
  }
);

SunriseAnimation.displayName = 'SunriseAnimation';

export default SunriseAnimation;
